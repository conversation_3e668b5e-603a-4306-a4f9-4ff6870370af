<template>
  <div class="patent-upload-view">
    <div class="container-fluid py-4">
      <div class="row justify-content-center">
        <div class="col-lg-8">
          <!-- Page Header -->
          <div class="d-flex align-items-center mb-4">
            <i class="bi bi-upload text-primary me-3" style="font-size: 2rem;"></i>
            <div>
              <h2 class="mb-1">专利上传</h2>
              <p class="text-muted mb-0">上传您的专利信息和相关文档</p>
            </div>
          </div>

          <!-- Upload Form -->
          <div class="card shadow-sm">
            <div class="card-body p-4">
              <form @submit.prevent="submitPatent">
                <!-- Basic Information -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-info-circle me-2"></i>
                      基本信息
                    </h5>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="patentName" class="form-label">专利名称 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="patentName"
                      v-model="form.patentName"
                      required
                      placeholder="请输入专利名称"
                    >
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="patentNumber" class="form-label">专利号 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="patentNumber"
                      v-model="form.patentNumber"
                      required
                      placeholder="例如：CN202410001234.5"
                    >
                    <div class="form-text">
                      格式：CN + 9-15位数字，可选择性加上 .数字
                    </div>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="patentCategory" class="form-label">专利类别 *</label>
                    <select
                      class="form-select"
                      id="patentCategory"
                      v-model="form.patentCategory"
                      required
                    >
                      <option value="">请选择专利类别</option>
                      <option value="invention">发明专利</option>
                      <option value="utility">实用新型专利</option>
                      <option value="design">外观设计专利</option>
                    </select>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="transferPrice" class="form-label">转让价格 (ETH) *</label>
                    <input
                      type="number"
                      step="0.001"
                      class="form-control"
                      id="transferPrice"
                      v-model="form.transferPrice"
                      required
                      placeholder="0.000"
                    >
                  </div>

                  <div class="col-12 mb-3">
                    <label for="patentAbstract" class="form-label">专利摘要 *</label>
                    <textarea
                      class="form-control"
                      id="patentAbstract"
                      rows="4"
                      v-model="form.patentAbstract"
                      required
                      placeholder="请输入专利摘要（至少50个字符）"
                    ></textarea>
                    <div class="form-text">
                      当前字符数：{{ form.patentAbstract.length }} / 2000（最少50个字符）
                    </div>
                  </div>
                </div>

                <!-- Date Information -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-calendar me-2"></i>
                      日期信息
                    </h5>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="applicationDate" class="form-label">专利申请日期 *</label>
                    <input
                      type="date"
                      class="form-control"
                      id="applicationDate"
                      v-model="form.applicationDate"
                      required
                    >
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="expirationDate" class="form-label">专利权结束日期 *</label>
                    <input
                      type="date"
                      class="form-control"
                      id="expirationDate"
                      v-model="form.expirationDate"
                      required
                    >
                  </div>
                </div>

                <!-- Owner Information -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-person me-2"></i>
                      专利权人信息
                    </h5>
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="ownerName" class="form-label">专利权人姓名 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="ownerName"
                      v-model="form.ownerName"
                      required
                      placeholder="请输入专利权人姓名"
                    >
                  </div>

                  <div class="col-md-6 mb-3">
                    <label for="ownerIdNumber" class="form-label">专利权人身份证号 *</label>
                    <input
                      type="text"
                      class="form-control"
                      id="ownerIdNumber"
                      v-model="form.ownerIdNumber"
                      required
                      placeholder="例如：110101199001011234"
                    >
                    <div class="form-text">
                      18位身份证号码
                    </div>
                  </div>
                </div>

                <!-- Agency Information -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-building me-2"></i>
                      代理信息
                    </h5>
                  </div>

                  <div class="col-12 mb-3">
                    <div class="form-check">
                      <input
                        class="form-check-input"
                        type="checkbox"
                        id="isAgentSale"
                        v-model="form.isAgentSale"
                      >
                      <label class="form-check-label" for="isAgentSale">
                        是否为代理出售
                      </label>
                    </div>
                  </div>
                </div>

                <!-- Document Upload -->
                <div class="row mb-4">
                  <div class="col-12">
                    <h5 class="text-primary mb-3">
                      <i class="bi bi-file-earmark-arrow-up me-2"></i>
                      文档上传
                    </h5>
                  </div>

                  <div class="col-12 mb-3">
                    <label for="documentFile" class="form-label">
                      专利权证明文档 *
                    </label>
                    <input
                      type="file"
                      class="form-control"
                      id="documentFile"
                      @change="handlePatentFileChange"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      required
                    >
                    <div class="form-text">
                      支持格式：PDF, DOC, DOCX, JPG, JPEG, PNG，最大文件大小：10MB
                    </div>
                  </div>

                  <div v-if="selectedPatentFile" class="col-12 mb-3">
                    <div class="alert alert-info">
                      <i class="bi bi-file-earmark me-2"></i>
                      已选择专利文档：{{ selectedPatentFile.name }} ({{ formatFileSize(selectedPatentFile.size) }})
                    </div>
                  </div>

                  <div class="col-12 mb-3">
                    <label for="ownershipFile" class="form-label">
                      {{ form.isAgentSale ? '专利代理委托证明文档' : '专利所有权证明文档' }} *
                    </label>
                    <input
                      type="file"
                      class="form-control"
                      id="ownershipFile"
                      @change="handleOwnershipFileChange"
                      accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                      required
                    >
                    <div class="form-text">
                      支持格式：PDF, DOC, DOCX, JPG, JPEG, PNG，最大文件大小：10MB
                    </div>
                  </div>

                  <div v-if="selectedOwnershipFile" class="col-12 mb-3">
                    <div class="alert alert-info">
                      <i class="bi bi-file-earmark me-2"></i>
                      已选择所有权文档：{{ selectedOwnershipFile.name }} ({{ formatFileSize(selectedOwnershipFile.size) }})
                    </div>
                  </div>
                </div>

                <!-- Submit Buttons -->
                <div class="row">
                  <div class="col-12">
                    <div class="d-flex gap-3">
                      <button
                        type="submit"
                        class="btn btn-primary"
                        :disabled="isSubmitting || !selectedPatentFile || !selectedOwnershipFile"
                      >
                        <span v-if="isSubmitting" class="spinner-border spinner-border-sm me-2"></span>
                        <i v-else class="bi bi-upload me-2"></i>
                        {{ isSubmitting ? '上传中...' : '提交专利' }}
                      </button>

                      <button
                        type="button"
                        class="btn btn-outline-secondary"
                        @click="resetForm"
                        :disabled="isSubmitting"
                      >
                        <i class="bi bi-arrow-clockwise me-2"></i>
                        重置表单
                      </button>
                    </div>
                  </div>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { useAuthStore } from '@/stores/auth'
import { patentService } from '@/services/patentService'
import { notificationService } from '@/services/notificationService'
import { useRouter } from 'vue-router'

export default {
  name: 'PatentUploadView',
  setup() {
    const authStore = useAuthStore()
    const router = useRouter()

    const form = reactive({
      patentName: '',
      patentNumber: '',
      patentCategory: '',
      transferPrice: '',
      patentAbstract: '',
      applicationDate: '',
      expirationDate: '',
      ownerName: '',
      ownerIdNumber: '',
      isAgentSale: false
    })

    const selectedPatentFile = ref(null)
    const selectedOwnershipFile = ref(null)
    const isSubmitting = ref(false)

    const handlePatentFileChange = (event) => {
      const file = event.target.files[0]
      if (file) {
        // Validate file size (10MB max)
        if (file.size > 10 * 1024 * 1024) {
          alert('文件大小不能超过10MB')
          event.target.value = ''
          return
        }
        selectedPatentFile.value = file
      }
    }

    const handleOwnershipFileChange = (event) => {
      const file = event.target.files[0]
      if (file) {
        // Validate file size (10MB max)
        if (file.size > 10 * 1024 * 1024) {
          alert('文件大小不能超过10MB')
          event.target.value = ''
          return
        }
        selectedOwnershipFile.value = file
      }
    }

    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }

    const submitPatent = async () => {
      try {
        isSubmitting.value = true

        // Validate form
        if (!selectedPatentFile.value || !selectedOwnershipFile.value) {
          notificationService.addNotification({
            type: 'error',
            title: '上传失败',
            message: '请选择专利文档和所有权证明文档',
            severity: 'error'
          })
          return
        }

        if (!authStore.account) {
          notificationService.addNotification({
            type: 'error',
            title: '上传失败',
            message: '请先连接钱包',
            severity: 'error'
          })
          return
        }

        console.log('Submitting patent with user address:', authStore.account)
        console.log('Auth store state:', {
          isConnected: authStore.isConnected,
          account: authStore.account,
          userRole: authStore.userRole
        })

        // Create FormData for file upload
        const formData = new FormData()

        // Add patent information
        formData.append('patentName', form.patentName)
        formData.append('patentNumber', form.patentNumber)
        formData.append('patentCategory', form.patentCategory)
        formData.append('transferPrice', form.transferPrice)
        formData.append('patentAbstract', form.patentAbstract)
        formData.append('applicationDate', form.applicationDate)
        formData.append('expirationDate', form.expirationDate)
        formData.append('ownerName', form.ownerName)
        formData.append('ownerIdNumber', form.ownerIdNumber)
        formData.append('isAgentSale', form.isAgentSale)
        formData.append('uploaderAddress', authStore.account)

        // Add files
        formData.append('patentDocument', selectedPatentFile.value)
        formData.append('ownershipDocument', selectedOwnershipFile.value)

        console.log('FormData contents:')
        for (let [key, value] of formData.entries()) {
          console.log(key, value)
        }

        // Submit to backend
        const result = await patentService.uploadPatent(formData)

        if (result.success) {
          notificationService.addNotification({
            type: 'success',
            title: '上传成功',
            message: result.message || '专利上传成功，等待审核中...',
            severity: 'success'
          })

          resetForm()

          // Navigate to My Patents page after successful upload
          setTimeout(() => {
            router.push('/patents/my-patents')
          }, 2000)
        } else {
          throw new Error(result.message || '上传失败')
        }

      } catch (error) {
        console.error('Patent submission failed:', error)
        
        // Enhanced error handling for better debugging
        let errorTitle = '上传失败'
        let errorMessage = '专利上传失败，请重试'
        
        if (error.response?.data?.error) {
          const errorData = error.response.data.error
          
          switch (errorData.code) {
            case 'USER_NOT_REGISTERED':
              errorTitle = '用户未注册'
              errorMessage = '您的账户尚未注册，请先完成注册流程'
              console.error('User registration details:', errorData.details)
              break
              
            case 'USER_PROFILE_ERROR':
              errorTitle = '用户资料错误'
              errorMessage = '用户资料无法访问，请联系管理员'
              console.error('Profile error details:', errorData.details)
              break
              
            case 'AUTHORIZATION_ERROR':
              errorTitle = '权限错误'
              errorMessage = errorData.message || '您没有上传专利的权限'
              break
              
            case 'AUTHENTICATION_ERROR':
              errorTitle = '认证失败'
              errorMessage = '用户地址验证失败，请重新连接钱包'
              break
              
            default:
              errorMessage = errorData.message || '上传失败，请重试'
          }
        } else if (error.message) {
          if (error.message.includes('User does not exist')) {
            errorTitle = '用户不存在'
            errorMessage = '您的账户未在区块链中注册，请先注册'
          } else if (error.message.includes('network')) {
            errorTitle = '网络错误'
            errorMessage = '网络连接失败，请检查网络连接'
          } else {
            errorMessage = error.message
          }
        }
        
        notificationService.addNotification({
          type: 'error',
          title: errorTitle,
          message: errorMessage,
          severity: 'error'
        })
        
        // Log detailed error for debugging
        console.error('Full error details:', {
          error: error.message,
          response: error.response?.data,
          userAddress: authStore.account,
          timestamp: new Date().toISOString()
        })
      } finally {
        isSubmitting.value = false
      }
    }

    const resetForm = () => {
      Object.keys(form).forEach(key => {
        if (typeof form[key] === 'boolean') {
          form[key] = false
        } else {
          form[key] = ''
        }
      })
      selectedPatentFile.value = null
      selectedOwnershipFile.value = null
      document.getElementById('documentFile').value = ''
      document.getElementById('ownershipFile').value = ''
    }

    return {
      authStore,
      form,
      selectedPatentFile,
      selectedOwnershipFile,
      isSubmitting,
      handlePatentFileChange,
      handleOwnershipFileChange,
      formatFileSize,
      submitPatent,
      resetForm
    }
  }
}
</script>

<style scoped>
.patent-upload-view {
  background-color: #f8f9fa;
  min-height: 100vh;
}

.card {
  border: none;
  border-radius: 0.75rem;
}

.form-label {
  font-weight: 600;
  color: #495057;
}

.form-control:focus,
.form-select:focus {
  border-color: #0d6efd;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.text-primary {
  color: #0d6efd !important;
}

.alert-info {
  background-color: #e7f3ff;
  border-color: #b8daff;
  color: #0c5460;
}
</style>
